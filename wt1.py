import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_long_jump(file_path, athlete_name):
    """
    分析立定跳远运动，确定起跳和落地时刻
    """
    # 读取数据
    df = pd.read_excel(file_path, sheet_name=0, header=0)

    # 提取关键点坐标
    # 脚部关键点：29-左脚跟, 30-右脚跟, 31-左脚趾, 32-右脚趾
    left_heel_x = df['29_X'].values
    left_heel_y = df['29_Y'].values
    right_heel_x = df['30_X'].values
    right_heel_y = df['30_Y'].values
    left_toe_x = df['31_X'].values
    left_toe_y = df['31_Y'].values
    right_toe_x = df['32_X'].values
    right_toe_y = df['32_Y'].values

    # 计算双脚的平均高度（Y坐标，注意图像坐标系Y轴向下为正）
    feet_y = (left_heel_y + right_heel_y + left_toe_y + right_toe_y) / 4

    # 计算重心位置（使用多个关键点）
    # 0-鼻子, 1-左眼, 2-右眼, 5-左肩, 6-右肩, 11-左髋, 12-右髋, 13-左膝, 14-右膝
    center_x = (df['5_X'] + df['6_X'] + df['11_X'] + df['12_X']) / 4  # 躯干中心X
    center_y = (df['5_Y'] + df['6_Y'] + df['11_Y'] + df['12_Y']) / 4  # 躯干中心Y

    # 平滑处理数据
    feet_y_smooth = gaussian_filter1d(feet_y, sigma=2)
    center_y_smooth = gaussian_filter1d(center_y, sigma=2)

    # 计算脚部高度变化率
    feet_velocity = np.gradient(feet_y_smooth)
    feet_acceleration = np.gradient(feet_velocity)

    return df, feet_y_smooth, center_y_smooth, feet_velocity, feet_acceleration

def detect_takeoff_landing(feet_y, feet_velocity, feet_acceleration, threshold_height=10, threshold_velocity=5):
    """
    检测起跳和落地时刻
    """
    # 起跳检测：脚部高度开始显著上升的时刻
    # 寻找脚部高度的最小值（起跳前的准备阶段）
    min_height_idx = np.argmin(feet_y[:len(feet_y)//2])  # 在前半段寻找最低点

    # 从最低点开始，寻找高度开始上升的时刻
    takeoff_candidates = []
    for i in range(min_height_idx, len(feet_y)-10):
        if feet_velocity[i] < -threshold_velocity:  # 向上运动（Y坐标减小）
            takeoff_candidates.append(i)
            break

    # 落地检测：脚部高度开始显著下降后重新接触地面
    # 寻找最高点
    max_height_idx = np.argmin(feet_y[len(feet_y)//3:]) + len(feet_y)//3

    # 从最高点开始寻找落地时刻
    landing_candidates = []
    for i in range(max_height_idx, len(feet_y)-5):
        if i > max_height_idx + 10:  # 确保有足够的滞空时间
            # 检查是否开始接触地面（高度变化趋于稳定）
            if abs(feet_velocity[i]) < 2 and i < len(feet_y) - 10:
                # 检查后续几帧是否保持稳定
                stable = True
                for j in range(i, min(i+5, len(feet_y))):
                    if abs(feet_velocity[j]) > 3:
                        stable = False
                        break
                if stable:
                    landing_candidates.append(i)
                    break

    takeoff_frame = takeoff_candidates[0] if takeoff_candidates else None
    landing_frame = landing_candidates[0] if landing_candidates else None

    return takeoff_frame, landing_frame

def plot_analysis(df, feet_y, center_y, feet_velocity, takeoff_frame, landing_frame, athlete_name):
    """
    绘制分析结果
    """
    frames = df['帧号'].values

    fig, axes = plt.subplots(3, 1, figsize=(12, 10))

    # 绘制脚部高度变化
    axes[0].plot(frames, feet_y, 'b-', linewidth=2, label='脚部平均高度')
    axes[0].plot(frames, center_y, 'g-', linewidth=2, label='重心高度')

    if takeoff_frame is not None:
        axes[0].axvline(x=frames[takeoff_frame], color='red', linestyle='--', linewidth=2, label=f'起跳时刻 (帧{frames[takeoff_frame]})')
    if landing_frame is not None:
        axes[0].axvline(x=frames[landing_frame], color='orange', linestyle='--', linewidth=2, label=f'落地时刻 (帧{frames[landing_frame]})')

    axes[0].set_xlabel('帧号')
    axes[0].set_ylabel('高度 (像素)')
    axes[0].set_title(f'{athlete_name} - 高度变化分析')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    axes[0].invert_yaxis()  # 反转Y轴，因为图像坐标系Y向下为正

    # 绘制速度变化
    axes[1].plot(frames, feet_velocity, 'r-', linewidth=2, label='脚部垂直速度')
    axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.3)

    if takeoff_frame is not None:
        axes[1].axvline(x=frames[takeoff_frame], color='red', linestyle='--', linewidth=2)
    if landing_frame is not None:
        axes[1].axvline(x=frames[landing_frame], color='orange', linestyle='--', linewidth=2)

    axes[1].set_xlabel('帧号')
    axes[1].set_ylabel('垂直速度 (像素/帧)')
    axes[1].set_title('垂直速度变化')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # 绘制运动轨迹
    left_toe_x = df['31_X'].values
    right_toe_x = df['32_X'].values
    center_x = (df['5_X'] + df['6_X'] + df['11_X'] + df['12_X']) / 4

    axes[2].plot(center_x, center_y, 'g-', linewidth=2, label='重心轨迹', alpha=0.7)
    axes[2].plot((left_toe_x + right_toe_x)/2, feet_y, 'b-', linewidth=2, label='脚部轨迹', alpha=0.7)

    if takeoff_frame is not None:
        axes[2].plot(center_x[takeoff_frame], center_y[takeoff_frame], 'ro', markersize=8, label='起跳位置')
    if landing_frame is not None:
        axes[2].plot(center_x[landing_frame], center_y[landing_frame], 'o', color='orange', markersize=8, label='落地位置')

    axes[2].set_xlabel('水平位置 (像素)')
    axes[2].set_ylabel('垂直位置 (像素)')
    axes[2].set_title('运动轨迹')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    axes[2].invert_yaxis()

    plt.tight_layout()
    plt.show()

    return fig

# 分析运动者1
print("正在分析运动者1...")
df1, feet_y1, center_y1, feet_velocity1, feet_acceleration1 = analyze_long_jump('运动者1的跳远位置信息.xlsx', '运动者1')
takeoff1, landing1 = detect_takeoff_landing(feet_y1, feet_velocity1, feet_acceleration1)

print(f"运动者1分析结果:")
if takeoff1 is not None:
    print(f"  起跳时刻: 第{df1['帧号'].iloc[takeoff1]}帧")
else:
    print("  起跳时刻: 未检测到")

if landing1 is not None:
    print(f"  落地时刻: 第{df1['帧号'].iloc[landing1]}帧")
    if takeoff1 is not None:
        airtime1 = landing1 - takeoff1
        print(f"  滞空时间: {airtime1}帧")
else:
    print("  落地时刻: 未检测到")

# 绘制分析图
fig1 = plot_analysis(df1, feet_y1, center_y1, feet_velocity1, takeoff1, landing1, '运动者1')

print("\n" + "="*50 + "\n")

# 分析运动者2
print("正在分析运动者2...")
df2, feet_y2, center_y2, feet_velocity2, feet_acceleration2 = analyze_long_jump('运动者2的跳远位置信息.xlsx', '运动者2')
takeoff2, landing2 = detect_takeoff_landing(feet_y2, feet_velocity2, feet_acceleration2)

print(f"运动者2分析结果:")
if takeoff2 is not None:
    print(f"  起跳时刻: 第{df2['帧号'].iloc[takeoff2]}帧")
else:
    print("  起跳时刻: 未检测到")

if landing2 is not None:
    print(f"  落地时刻: 第{df2['帧号'].iloc[landing2]}帧")
    if takeoff2 is not None:
        airtime2 = landing2 - takeoff2
        print(f"  滞空时间: {airtime2}帧")
else:
    print("  落地时刻: 未检测到")

# 绘制分析图
fig2 = plot_analysis(df2, feet_y2, center_y2, feet_velocity2, takeoff2, landing2, '运动者2')

print("\n" + "="*50)
print("滞空阶段运动过程分析")
print("="*50)

def analyze_flight_phase(df, takeoff_frame, landing_frame, athlete_name):
    """
    分析滞空阶段的运动过程
    """
    if takeoff_frame is None or landing_frame is None:
        print(f"{athlete_name}: 无法分析滞空阶段，起跳或落地时刻未检测到")
        return

    print(f"\n{athlete_name}滞空阶段分析:")

    # 提取滞空阶段数据
    flight_data = df.iloc[takeoff_frame:landing_frame+1]

    # 计算重心轨迹
    center_x = (flight_data['5_X'] + flight_data['6_X'] + flight_data['11_X'] + flight_data['12_X']) / 4
    center_y = (flight_data['5_Y'] + flight_data['6_Y'] + flight_data['11_Y'] + flight_data['12_Y']) / 4

    # 计算水平和垂直位移
    horizontal_displacement = center_x.iloc[-1] - center_x.iloc[0]
    max_height = center_y.min()  # Y坐标最小值对应最高点
    initial_height = center_y.iloc[0]
    height_gain = initial_height - max_height  # 高度增益

    # 计算速度
    time_frames = len(flight_data)
    horizontal_velocity = horizontal_displacement / time_frames

    # 找到最高点时刻
    max_height_frame = center_y.idxmin() - takeoff_frame

    print(f"  滞空时间: {time_frames}帧")
    print(f"  水平位移: {horizontal_displacement:.2f}像素")
    print(f"  最大高度增益: {height_gain:.2f}像素")
    print(f"  平均水平速度: {horizontal_velocity:.2f}像素/帧")
    print(f"  到达最高点时刻: 起跳后第{max_height_frame}帧")

    # 分析运动阶段
    print(f"  运动阶段分析:")
    print(f"    上升阶段: 第{takeoff_frame}帧 - 第{takeoff_frame + max_height_frame}帧")
    print(f"    下降阶段: 第{takeoff_frame + max_height_frame}帧 - 第{landing_frame}帧")

    return {
        'flight_time': time_frames,
        'horizontal_displacement': horizontal_displacement,
        'height_gain': height_gain,
        'horizontal_velocity': horizontal_velocity,
        'max_height_frame': max_height_frame
    }

# 分析两位运动者的滞空阶段
flight_analysis1 = analyze_flight_phase(df1, takeoff1, landing1, '运动者1')
flight_analysis2 = analyze_flight_phase(df2, takeoff2, landing2, '运动者2')

# 比较分析
print(f"\n比较分析:")
if flight_analysis1 and flight_analysis2:
    print(f"滞空时间比较: 运动者1({flight_analysis1['flight_time']}帧) vs 运动者2({flight_analysis2['flight_time']}帧)")
    print(f"水平位移比较: 运动者1({flight_analysis1['horizontal_displacement']:.2f}像素) vs 运动者2({flight_analysis2['horizontal_displacement']:.2f}像素)")
    print(f"高度增益比较: 运动者1({flight_analysis1['height_gain']:.2f}像素) vs 运动者2({flight_analysis2['height_gain']:.2f}像素)")

# 读取实际成绩进行对比
with open('运动者1和运动者2的跳远成绩.txt', 'r', encoding='utf-8') as f:
    scores = f.read()
    print(f"\n实际跳远成绩:")
    print(scores)
